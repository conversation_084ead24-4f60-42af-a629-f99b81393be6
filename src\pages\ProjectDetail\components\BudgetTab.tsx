import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DollarSign,
  Receipt,
  CheckCircle,
  TrendingUp,
  Pie<PERSON>hart,
} from "lucide-react";
import { Loading } from "@/components/ui/loaders";
import { formatDate } from "@/utils";
import {
  getCategoryIcon,
  getCategoryColor,
  getStatusColor,
  formatVND,
} from "../shared/utils";

// Local types
interface BudgetData {
  total: number;
  spent: number;
  allocated: {
    personnel: number;
    equipment: number;
    materials: number;
    other: number;
  };
  expenses: ExpenseItem[];
}

interface ExpenseItem {
  id: string;
  category: "personnel" | "equipment" | "materials" | "other";
  description: string;
  amount: number;
  date: string;
  status: "Pending" | "Approved" | "Rejected";
  receipt?: string;
  approvedBy?: string;
  feedback?: string;
}

// Utility functions
// Only keep getCategoryIcon, getCategoryColor, getStatusColor as local helpers
// Removed local helper function definitions

const BudgetTab: React.FC = () => {
  const [budget, setBudget] = useState<BudgetData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadBudgetData();
  }, []);

  const loadBudgetData = async () => {
    setIsLoading(true);
    try {
      // Simulate API call
      setTimeout(() => {
        const mockBudget: BudgetData = {
          total: 2880000000, // ~120K USD in VND
          spent: 1872000000, // ~78K USD in VND
          allocated: {
            personnel: 1440000000, // ~60K USD in VND
            equipment: 720000000, // ~30K USD in VND
            materials: 360000000, // ~15K USD in VND
            other: 120000000, // ~5K USD in VND
          },
          expenses: [
            {
              id: "1",
              category: "personnel",
              description: "Research Assistant Salary - Q1",
              amount: 360000000, // ~15K USD in VND
              date: "2024-03-31",
              status: "Approved",
              receipt: "salary-receipt-q1.pdf",
              approvedBy: "Finance Department",
            },
            {
              id: "2",
              category: "equipment",
              description: "High-Performance Computing Server",
              amount: 600000000, // ~25K USD in VND
              date: "2024-04-15",
              status: "Approved",
              receipt: "server-invoice.pdf",
              approvedBy: "Finance Department",
            },
            {
              id: "4",
              category: "materials",
              description: "Research Materials and Supplies",
              amount: 67200000, // ~2.8K USD in VND
              date: "2024-06-01",
              status: "Rejected",
              receipt: "materials-invoice.pdf",
              feedback:
                "Please provide more detailed breakdown of materials needed",
            },
          ],
        };
        setBudget(mockBudget);
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error("Error loading budget data:", error);
      setIsLoading(false);
    }
  };

  const getStatusStats = () => {
    if (!budget) return { approved: 0, pending: 0, rejected: 0 };

    return {
      approved: budget.expenses.filter((e) => e.status === "Approved").length,
      pending: budget.expenses.filter((e) => e.status === "Pending").length,
      rejected: budget.expenses.filter((e) => e.status === "Rejected").length,
    };
  };

  const getBudgetUtilization = () => {
    if (!budget) return 0;
    return Math.round((budget.spent / budget.total) * 100);
  };

  const stats = getStatusStats();
  const utilization = getBudgetUtilization();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loading />
      </div>
    );
  }

  if (!budget) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No budget data available.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4 sm:pb-6">
        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 sm:gap-4">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg sm:text-xl font-semibold text-gray-900">
              Budget Overview
            </CardTitle>
            <CardDescription className="text-sm sm:text-base mt-1">
              Project budget allocation and expense tracking
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6 pt-0">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg border border-green-100">
            <DollarSign className="w-6 h-6 text-green-600" />
            <div>
              <p className="text-xl font-semibold text-gray-900">
                {formatVND(budget.total)}
              </p>
              <p className="text-sm text-gray-600 font-medium">Total Budget</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <TrendingUp className="w-6 h-6 text-blue-600" />
            <div>
              <p className="text-xl font-bold text-gray-900">
                {formatVND(budget.spent)}
              </p>
              <p className="text-sm text-gray-600 font-medium">
                Spent ({utilization}%)
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-4 bg-purple-50 rounded-lg border border-purple-100">
            <PieChart className="w-6 h-6 text-purple-600" />
            <div>
              <p className="text-xl font-bold text-gray-900">
                {formatVND(budget.total - budget.spent)}
              </p>
              <p className="text-sm text-gray-600 font-medium">Remaining</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-4 bg-emerald-50 rounded-lg border border-emerald-100">
            <CheckCircle className="w-6 h-6 text-emerald-600" />
            <div>
              <p className="text-xl font-bold text-gray-900">
                {stats.approved}
              </p>
              <p className="text-sm text-gray-600 font-medium">
                Approved Expenses
              </p>
            </div>
          </div>
        </div>

        <Separator className="my-4 sm:my-6" />

        {/* Budget Allocation Section */}
        <div>
          <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-3 sm:mb-4">
            Budget Allocation
          </h3>
          <div className="space-y-3 sm:space-y-4">
            {Object.entries(budget.allocated).map(([category, amount]) => {
              const spent = budget.expenses
                .filter(
                  (e) => e.category === category && e.status === "Approved"
                )
                .reduce((sum, e) => sum + e.amount, 0);
              const percentage = Math.round((spent / (amount as number)) * 100);

              return (
                <div key={category} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">
                        {getCategoryIcon(category)}
                      </span>
                      <span className="font-medium capitalize text-sm sm:text-base">
                        {category}
                      </span>
                      <Badge
                        className={`${getCategoryColor(category)} text-xs`}
                      >
                        {formatVND(spent)} / {formatVND(amount as number)}
                      </Badge>
                    </div>
                    <span className="text-xs sm:text-sm text-muted-foreground">
                      {percentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <Separator className="my-4 sm:my-6" />

        {/* Expenses Table Section */}
        <div>
          <h3 className="text-sm sm:text-base font-medium text-gray-700 mb-3 sm:mb-4">
            Recent Expenses
          </h3>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[200px]">Description</TableHead>
                  <TableHead className="min-w-[100px]">Category</TableHead>
                  <TableHead className="min-w-[80px]">Amount</TableHead>
                  <TableHead className="min-w-[100px]">Date</TableHead>
                  <TableHead className="min-w-[80px]">Status</TableHead>
                  <TableHead className="text-right min-w-[80px]">
                    Receipt
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {budget.expenses.slice(0, 5).map((expense) => (
                  <TableRow key={expense.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium text-sm sm:text-base break-words">
                          {expense.description}
                        </p>
                        {expense.feedback && (
                          <p className="text-xs sm:text-sm text-red-600 mt-1">
                            {expense.feedback}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1 sm:space-x-2">
                        <span className="text-sm">
                          {getCategoryIcon(expense.category)}
                        </span>
                        <Badge
                          className={`${getCategoryColor(
                            expense.category
                          )} text-xs`}
                        >
                          {expense.category}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatVND(expense.amount)}
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(expense.date)}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={`${getStatusColor(expense.status)} text-xs`}
                      >
                        {expense.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {expense.receipt && (
                        <Badge variant="outline" className="text-xs">
                          <Receipt className="w-3 h-3 mr-1" />
                          <span className="hidden sm:inline">Receipt</span>
                        </Badge>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {budget.expenses.length === 0 && (
              <p className="text-muted-foreground text-center py-8 text-sm sm:text-base">
                No expenses recorded yet.
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BudgetTab;
